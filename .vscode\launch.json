{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/桌面/WHEELTEC_C07A-LF04四路红外巡线差速车(TI芯片MSPM0G3507实现)/KEIL5/WHEELTEC_C07A_IRF_CAR/WHEELTEC_C07A_IRF_CAR", "program": "d:/桌面/WHEELTEC_C07A-LF04四路红外巡线差速车(TI芯片MSPM0G3507实现)/KEIL5/WHEELTEC_C07A_IRF_CAR/WHEELTEC_C07A_IRF_CAR/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}